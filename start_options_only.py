#!/usr/bin/env python3
"""
独立期权交易启动脚本

只执行期权套利分析和交易，不执行股票交易。
基于ML验证的相关性背离进行期权套利。
"""

import argparse
import asyncio
import logging
import os
import sys
import traceback
from datetime import datetime

# 导入统一配置
from config import DEFAULT_CONFIG
from core.exceptions import (
    ErrorCategory,
    ErrorSeverity,
    handle_errors
)

# 设置日志 - 与 start_full_trading.py 保持一致
def setup_logging():
    """设置日志"""
    # 使用统一的根目录logs文件夹
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)

    # 配置日志 - 使用统一的日志文件
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(f"{log_dir}/trading_system.log"),
            logging.StreamHandler(sys.stdout),
        ],
    )

    return logging.getLogger(__name__)

logger = setup_logging()


@handle_errors(default_return=False, error_category=ErrorCategory.TRADING)
async def run_options_only_workflow(args):
    """独立期权交易工作流程"""
    logger.info("🎯 启动独立期权交易系统")
    logger.info("=" * 60)
    logger.info(f"💰 资金: ${args.capital:,.2f}")
    logger.info(f"📊 交易模式: {'纸上交易' if args.paper else '实盘交易'}")
    logger.info("=" * 60)
    # 导入必要模块
    from config import DEFAULT_CONFIG
    from options.arbitrage import OptionsArbitrageSystem
    from stocks.trend import DailyTradingSystem

    # 设置IBKR配置 - 与 start_full_trading.py 保持一致
    ibkr_config = DEFAULT_CONFIG.ibkr
    ibkr_config.paper_trading = args.paper
    ibkr_config.port = 7497 if args.paper else 7496

    # 显示优化配置
    from options.params import get_optimization_summary

    print("\n" + "=" * 60)
    print("🎯 优化期权配置")
    print("=" * 60)
    print(get_optimization_summary())

    # 第一步：获取股票数据和ML模型分析结果
    logger.info("📊 第1步：获取股票数据和ML模型分析...")

    # 创建交易系统实例（仅用于数据获取）
    trading_system = DailyTradingSystem(
        config=ibkr_config,
    )

    # 下载最新股票数据（交易系统必须使用最新数据）
    logger.info("📥 下载最新股票数据...")
    download_success = await trading_system.step1_download_all_stocks()
    if not download_success:
        logger.error("❌ 数据下载失败")
        return False

    # 运行ML模型分析
    logger.info("🧠 运行ML模型分析...")
    model_success = trading_system.step2_run_model_analysis()
    if not model_success:
        logger.error("❌ ML模型分析失败")
        return False

    # 获取模型分析结果
    model_results = trading_system.model_results

    # 运行相关性分析
    logger.info("🔗 运行相关性分析...")
    correlation_success = trading_system.step3_correlation_analysis()
    if not correlation_success:
        logger.error("❌ 相关性分析失败")
        return False

    # 获取相关性分析结果
    correlation_analysis = trading_system.correlation_analysis
    if not correlation_analysis:
        logger.error("❌ 无法获取相关性分析结果")
        return False

    if not model_results:
        logger.error("❌ 无法获取模型分析结果")
        return False

    # 第二步：期权套利分析
    logger.info("🎯 第2步：期权套利分析...")

    # 创建期权套利系统，复用已连接的IBKR客户端
    # 使用与完整交易系统一致的期权图表目录
    options_charts_dir = f"results/{datetime.now().strftime('%Y%m%d')}/options_charts"
    os.makedirs(options_charts_dir, exist_ok=True)

    options_system = OptionsArbitrageSystem(
        config=ibkr_config,
        charts_dir=options_charts_dir,
        ibkr_client=trading_system.ibkr_client  # 复用已连接的客户端
    )

    # 运行期权套利
    options_success = await options_system.run_options_arbitrage(
        correlation_analysis, model_results, args.capital
    )

    if options_success:
        logger.info("✅ 期权交易完成")
    else:
        logger.warning("⚠️ 期权交易未执行或失败")

    logger.info("🎉 独立期权交易工作流程完成")
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="独立期权交易系统启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 纸上期权交易（使用最新市场数据）
  python start_options_only.py --paper --capital 100000

  # 实盘期权交易（使用最新市场数据）
  python start_options_only.py --live --capital 50000
        """,
    )

    # 交易模式
    trading_group = parser.add_mutually_exclusive_group(required=True)
    trading_group.add_argument(
        "--paper", action="store_true", help="纸上交易模式 (IBKR端口7497)"
    )
    trading_group.add_argument(
        "--live", action="store_true", help="实盘交易模式 (IBKR端口7496)"
    )

    # 资金设置
    parser.add_argument(
        "--capital", type=float, default=100000.0, help="期权交易资金 (默认: 100000)"
    )

    args = parser.parse_args()

    # 确保结果目录存在 - 与 start_full_trading.py 保持一致
    today = datetime.now().strftime("%Y%m%d")
    os.makedirs(f"results/{today}", exist_ok=True)

    # 运行独立期权交易工作流程
    try:
        success = asyncio.run(run_options_only_workflow(args))
        return 0 if success else 1

    except KeyboardInterrupt:
        print("\n⏹️ 期权交易工作流被用户中断")
        # 清理任何未完成的协程
        try:
            # 获取当前事件循环中的所有任务
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                # 如果没有运行的事件循环，尝试获取默认循环
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError:
                    print("⚠️ 无法获取事件循环进行清理")
                    return 1

            pending_tasks = [task for task in asyncio.all_tasks(loop) if not task.done()]
            if pending_tasks:
                print(f"🧹 清理 {len(pending_tasks)} 个未完成的任务...")
                # 取消所有未完成的任务
                for task in pending_tasks:
                    task.cancel()
                # 等待所有任务完成取消
                try:
                    loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
                except RuntimeError:
                    # 如果循环已经关闭，直接忽略
                    print("⚠️ 事件循环已关闭，跳过任务清理")
        except Exception as cleanup_error:
            print(f"⚠️ 清理过程中出现错误: {cleanup_error}")
        return 1
    except Exception as e:
        print(f"\n💥 意外错误: {e}")
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
