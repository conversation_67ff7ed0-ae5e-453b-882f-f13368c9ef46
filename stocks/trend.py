#!/usr/bin/env python
"""

智能股票交易系统的核心分析和交易模块，专注于机器学习模型分析、相关性计算和交易决策。
工作流程：加载缓存数据 -> 模型分析 -> 相关性分析 -> 交易决策。
提供多种ML模型（<PERSON>、<PERSON>、<PERSON>、<PERSON><PERSON>、<PERSON>、<PERSON>）进行股票趋势预测和相关性背离分析。

注意：需要先运行 updater.py 来准备股票数据，此模块专注于分析和交易逻辑。
"""

import argparse
import asyncio
import logging
import os
import pickle
import shutil
from datetime import datetime
from typing import Dict

import numpy as np
import pandas as pd

from stocks.bots import <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>
from core.broker import get_ibkr_client
from config import DEFAULT_CONFIG
from config import IBKRConfig
from core.models import train_msis_mcs
from core.stats import estimate_logprice_statistics, estimate_price_statistics
from core.utils import extract_hierarchical_info
from stocks.portfolio import PortfolioManager
from core.exceptions import (
    ErrorCategory,
    handle_errors,
    async_handle_errors
)

# 设置日志 - 将在DailyTradingSystem.__init__中重新配置
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DailyTradingSystem:
    """
    Daily Trading System optimized workflow:
    1. Load cached stock data (prepared by updater.py)
    2. Run ML model to identify trends
    3. Perform correlation analysis
    4. Execute trades based on correlation results
    """

    def __init__(
        self,
        config: IBKRConfig = None,
        bot_class=Adam,
        initial_capital: float = 10000.0,
        extreme_level: str = "high",
        enable_options: bool = False,
    ):
        self.config = config or DEFAULT_CONFIG.ibkr
        # 始终启用真实交易 - IBKR端口决定纸上交易或实盘模式
        self.bot = bot_class(
            initial_capital, real_trading=True, ibkr_config=self.config
        )
        self.ibkr_client = get_ibkr_client("default", self.config)
        self.extreme_level = extreme_level
        self.enable_options = enable_options

        # 测试模式属性
        self.test_mode = False
        self.test_symbols = None

        # 启动时清理旧缓存文件
        self._cleanup_old_cache_files()

        # 系统状态
        self.all_stocks_data = None
        self.model_results = None
        self.correlation_analysis = None
        self.trading_decisions = None
        self.portfolio_manager = None

        # 文件路径 - 所有数据文件使用data/cache
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.model_cache_file = f"data/cache/temp/daily_model_cache_{timestamp}.pickle"

        # Results files go in organized directories
        date_str = datetime.now().strftime("%Y%m%d")
        self.results_file = f"results/{date_str}/daily_results_{date_str}.csv"
        self.charts_dir = f"results/{date_str}/charts"
        self.options_charts_dir = f"results/{date_str}/options_charts"
        # 使用统一的根目录logs文件夹
        self.logs_dir = "logs"

        # Create results directory structure (在设置logs_dir之后)
        self._setup_results_directories()

        # Setup organized logging
        self._setup_logging()

    def _cleanup_old_cache_files(self):
        """清理旧缓存文件以防止冲突"""
        try:
            # Remove old model cache files (keep only current session)
            for filename in os.listdir("."):
                if filename.startswith("daily_model_cache_") and filename.endswith(
                    ".pickle"
                ):
                    if hasattr(
                        self, "model_cache_file"
                    ) and filename != os.path.basename(self.model_cache_file):
                        os.remove(filename)
                        logger.info(f"删除旧缓存文件: {filename}")

                # Also remove generic cache files that might cause conflicts
                if filename in ["daily_model_cache.pickle"]:
                    os.remove(filename)
                    logger.info(f"删除冲突缓存文件: {filename}")

        except Exception as e:
            logger.warning(f"缓存清理警告: {e}")

    @handle_errors(default_return=None, error_category=ErrorCategory.SYSTEM)
    def _setup_results_directories(self):
        """为结果、图表和数据设置有组织的目录结构"""
        date_str = datetime.now().strftime("%Y%m%d")

        # Create main directories
        main_dirs = ["results"]
        for main_dir in main_dirs:
            if not os.path.exists(main_dir):
                os.makedirs(main_dir)
                logger.info(f"📁 Created main directory: {main_dir}")

        # Create date-specific directory
        date_dir = f"results/{date_str}"
        if not os.path.exists(date_dir):
            os.makedirs(date_dir)
            logger.info(f"📁 Created date directory: {date_dir}")

        # Create subdirectories for different types of files (不包括logs，使用统一的根目录logs)
        subdirs = ["charts", "options_charts"]
        for subdir in subdirs:
            subdir_path = f"{date_dir}/{subdir}"
            if not os.path.exists(subdir_path):
                os.makedirs(subdir_path)
                logger.info(f"📁 Created subdirectory: {subdir_path}")

        # 确保统一的根目录logs文件夹存在
        if not os.path.exists(self.logs_dir):
            os.makedirs(self.logs_dir)
            logger.info(f"📁 Created unified logs directory: {self.logs_dir}")

    def _setup_logging(self):
        """设置统一的日志记录到根目录logs文件夹"""
        try:
            # Create log file path - 使用统一的根目录logs文件夹和统一的日志文件名
            log_file = f"{self.logs_dir}/trading_system.log"

            # Configure file handler
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.INFO)
            file_formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            file_handler.setFormatter(file_formatter)

            # Configure console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_formatter = logging.Formatter(
                "%(asctime)s - %(levelname)s - %(message)s"
            )
            console_handler.setFormatter(console_formatter)

            # Get root logger and configure
            root_logger = logging.getLogger()
            root_logger.handlers.clear()  # Clear existing handlers
            root_logger.addHandler(file_handler)
            root_logger.addHandler(console_handler)
            root_logger.setLevel(logging.INFO)

            logger.info(f"📝 Logging configured: {log_file}")

        except Exception as e:
            logger.warning(f"日志设置警告: {e}")

    def clean_cache_files(self):
        """清理缓存文件以强制使用优化顺序方法进行新下载"""
        import glob

        # Clean all cache and data files from data/cache
        cache_patterns = [
            "data/cache/temp/*.pkl",
            "data/cache/temp/*.pickle",
            "data/cache/temp/*.json",
            "incremental_data.pkl",  # Legacy files in root
            "model_cache_*.pkl",  # Legacy files in root
            "daily_model_cache_*.pickle",  # Legacy files in root
            "data.pickle",  # Legacy files in root
            "download_progress.json",  # Legacy files in root
        ]

        logger.info("🧹 清理缓存文件以优化顺序下载...")

        removed_count = 0
        for pattern in cache_patterns:
            if "*" in pattern:
                for file in glob.glob(pattern):
                    if os.path.exists(file):
                        os.remove(file)
                        logger.info(f"   Removed: {file}")
                        removed_count += 1
            else:
                if os.path.exists(pattern):
                    os.remove(pattern)
                    logger.info(f"   Removed: {pattern}")
                    removed_count += 1

        logger.info(
            f"✅ Cache cleaned - removed {removed_count} files. System will use fresh download."
        )



    def step1_load_stock_data(self) -> bool:
        """步骤1: 从缓存加载股票数据（假设updater.py已完成数据下载）"""
        logger.info("步骤 1: 加载股票数据进行分析...")
        
        try:
            # Load cached stock data
            import pickle
            from core.cache import get_cache_path
            
            # Try to load cached data from updater.py
            cache_file = get_cache_path("stock_data", "stock_data_1y.pkl")
            
            if not os.path.exists(cache_file):
                logger.error(f"未找到缓存数据文件: {cache_file}")
                logger.error("请先运行数据更新 (updater.py) 来生成缓存数据")
                return False
            
            logger.info(f"从缓存加载股票数据: {cache_file}")
            
            with open(cache_file, "rb") as f:
                self.all_stocks_data = pickle.load(f)
            
            if not self.all_stocks_data or len(self.all_stocks_data.get("tickers", [])) == 0:
                logger.error("缓存数据为空或格式错误")
                return False
            
            logger.info(f"成功加载 {len(self.all_stocks_data['tickers'])} 支股票数据")
            if self.all_stocks_data.get('dates'):
                logger.info(f"数据期间: {self.all_stocks_data['dates'][0]} 到 {self.all_stocks_data['dates'][-1]}")
            
            return True
            
        except Exception as e:
            import traceback
            logger.error(f"步骤 1 加载数据出错: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

    async def step1_download_all_stocks(self) -> bool:
        """步骤1: 强制下载最新股票数据（替代缓存加载）"""
        logger.info("步骤 1: 强制下载最新股票数据...")

        try:
            from core.market_data import download_with_ibkr
            import os

            # 获取股票列表 - 使用统一配置
            symbols_file = DEFAULT_CONFIG.system.symbols_file
            if not os.path.exists(symbols_file):
                logger.error(f"股票列表文件不存在: {symbols_file}")
                logger.error("请先运行 update_stock_universe.py 生成股票列表")
                return False

            # 读取股票列表
            with open(symbols_file, 'r') as f:
                tickers = [line.strip() for line in f if line.strip()]

            if not tickers:
                logger.error("股票列表为空")
                return False

            logger.info(f"开始强制下载 {len(tickers)} 支股票的最新数据...")

            # 强制下载最新数据（不使用预取缓存）
            self.all_stocks_data = await download_with_ibkr(
                tickers,
                use_prefetch=False  # 强制下载最新数据
            )

            if not self.all_stocks_data or len(self.all_stocks_data.get("tickers", [])) == 0:
                logger.error("数据下载失败或为空")
                return False

            logger.info(f"成功下载 {len(self.all_stocks_data['tickers'])} 支股票数据")
            if self.all_stocks_data.get('dates'):
                logger.info(f"数据期间: {self.all_stocks_data['dates'][0]} 到 {self.all_stocks_data['dates'][-1]}")

            return True

        except Exception as e:
            import traceback
            logger.error(f"步骤 1 数据下载出错: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False

    @handle_errors(default_return=False, error_category=ErrorCategory.DATA)
    def step2_run_model_analysis(self) -> bool:
        """步骤2: 运行ML模型识别趋势上方/下方的股票"""
        logger.info("步骤 2: 运行机器学习模型分析...")
        if not self.all_stocks_data:
            logger.error("没有股票数据可用于模型分析")
            return False

        # Prepare data for model - handle invalid values
        prices = self.all_stocks_data["price"]

        # Handle case where prices is a list (due to inhomogeneous data)
        if isinstance(prices, list):
            logger.warning("价格数据是列表（检测到非同构shape）")
            logger.info(f"股票数量: {len(prices)}")

            # Find stocks with sufficient data for meaningful analysis
            if len(prices) > 0:
                lengths = [len(p) if hasattr(p, "__len__") else 0 for p in prices]
                min_length = min(lengths) if lengths else 0
                max_length = max(lengths) if lengths else 0
                logger.info(
                    f"Price array lengths: min={min_length}, max={max_length}"
                )

                # Set minimum required data points for scientific analysis
                min_required_days = 183  # Require at least 183 trading days (~9 months) for reliable correlation analysis

                if max_length < min_required_days:
                    logger.error(
                        f"Insufficient data: maximum length {max_length} < required {min_required_days}"
                    )
                    return False

                # Only use stocks with sufficient data
                sufficient_data_stocks = []
                valid_tickers = []
                valid_sectors = {}
                valid_industries = {}

                for i, price_array in enumerate(prices):
                    if (
                        hasattr(price_array, "__len__")
                        and len(price_array) >= min_required_days
                    ):
                        # Take the last min_required_days values (most recent data)
                        standardized_array = np.array(
                            price_array[-min_required_days:]
                        )
                        if np.any(np.isfinite(standardized_array)) and np.any(
                            standardized_array > 0
                        ):
                            sufficient_data_stocks.append(standardized_array)
                            # 确保索引在范围内
                            if i < len(self.all_stocks_data["tickers"]):
                                ticker = self.all_stocks_data["tickers"][i]
                                valid_tickers.append(ticker)
                                if ticker in self.all_stocks_data["sectors"]:
                                    valid_sectors[ticker] = self.all_stocks_data[
                                            "sectors"
                                        ][ticker]
                                    if ticker in self.all_stocks_data["industries"]:
                                        valid_industries[ticker] = self.all_stocks_data[
                                            "industries"
                                        ][ticker]
                            else:
                                logger.warning(f"索引 {i} 超出股票列表范围 ({len(self.all_stocks_data['tickers'])})")
                                # 使用默认股票名称
                                ticker = f"STOCK_{i}"
                                valid_tickers.append(ticker)

                # 循环结束后检查结果
                if len(sufficient_data_stocks) == 0:
                    logger.error("没有足够数据用于分析的股票")
                    return False

                # Convert to numpy array
                filtered_prices = np.array(sufficient_data_stocks)

                # Calculate rejected count before updating
                original_count = len(self.all_stocks_data["tickers"])
                rejected_count = original_count - len(valid_tickers)

                # Update the data structure
                self.all_stocks_data["price"] = filtered_prices
                self.all_stocks_data["tickers"] = valid_tickers
                self.all_stocks_data["sectors"] = valid_sectors
                self.all_stocks_data["industries"] = valid_industries

                logger.info(
                    f"✅ Selected {len(sufficient_data_stocks)} stocks with sufficient data ({min_required_days} days each)"
                )
                logger.info(
                    f"❌ Rejected {rejected_count} stocks with insufficient data (< {min_required_days} days)"
                )
                logger.info(
                    "📊 Data quality: Using only stocks with robust historical data for scientific analysis"
                )

                # Use the filtered data for further processing
                prices = filtered_prices
            else:
                logger.error("没有可用的价格数据")
                return False

            # Convert to numpy array if not already
            if not isinstance(prices, np.ndarray):
                prices = np.array(prices)

            logger.info(f"Initial data shape: {prices.shape}")
            logger.info(
                f"Price data range: min={np.nanmin(prices):.4f}, max={np.nanmax(prices):.4f}"
            )
            logger.info(
                f"NaN count: {np.sum(np.isnan(prices))}, Zero count: {np.sum(prices == 0)}"
            )

            # Advanced data cleaning and validation
            logger.info("执行高级数据清理...")

            # Step 1: Clean extreme outliers and invalid values
            cleaned_prices = prices.copy()

            # Remove negative prices and extreme outliers
            for i in range(len(cleaned_prices)):
                stock_prices = cleaned_prices[i]

                # Replace negative prices with NaN
                stock_prices[stock_prices <= 0] = np.nan

                # Remove extreme outliers (prices > $10,000 are likely errors)
                stock_prices[stock_prices > 10000] = np.nan

                # Forward fill small gaps (up to 5 consecutive NaN values)
                mask = np.isnan(stock_prices)
                if np.any(~mask):  # If there's at least some valid data
                    # Forward fill
                    last_valid = None
                    gap_count = 0
                    for j in range(len(stock_prices)):
                        if not mask[j]:
                            last_valid = stock_prices[j]
                            gap_count = 0
                        elif last_valid is not None and gap_count < 5:
                            stock_prices[j] = last_valid
                            gap_count += 1
                        else:
                            gap_count += 1

                cleaned_prices[i] = stock_prices

            # Step 2: Progressive validation criteria based on data quality
            total_nan_ratio = np.sum(np.isnan(cleaned_prices)) / cleaned_prices.size

            if total_nan_ratio > 0.4:  # If >40% data is missing, be very lenient
                min_valid_ratio = 0.3  # 降低到30%以包含更多股票
                min_recent_ratio = 0.2
                logger.warning(
                    f"High missing data rate ({total_nan_ratio:.1%}), using lenient validation"
                )
            elif (
                total_nan_ratio > 0.2
            ):  # If >20% data is missing, be moderately lenient
                min_valid_ratio = 0.4  # 降低到40%
                min_recent_ratio = 0.25
                logger.info(
                    f"Moderate missing data rate ({total_nan_ratio:.1%}), using moderate validation"
                )
            else:  # Good data quality, use stricter validation
                min_valid_ratio = 0.6  # 降低到60%
                min_recent_ratio = 0.4  # 降低到40%
                logger.info(
                    f"Good data quality ({total_nan_ratio:.1%}), using relaxed validation for more stocks"
                )

            recent_days = 20

            valid_mask = []
            for i in range(len(cleaned_prices)):
                stock_prices = cleaned_prices[i]
                ticker = self.all_stocks_data["tickers"][i]

                # Check overall validity
                valid_points = np.sum(np.isfinite(stock_prices) & (stock_prices > 0))
                valid_ratio = valid_points / len(stock_prices)

                # Check recent data validity
                recent_prices = stock_prices[-recent_days:]
                recent_valid = np.sum(np.isfinite(recent_prices) & (recent_prices > 0))
                recent_ratio = recent_valid / len(recent_prices)

                # Check if stock has reasonable price range
                if valid_points > 0:
                    valid_data = stock_prices[
                        np.isfinite(stock_prices) & (stock_prices > 0)
                    ]
                    price_range = np.max(valid_data) / np.min(valid_data)
                    reasonable_range = (
                        price_range < 100
                    )  # Price shouldn't vary more than 100x
                else:
                    reasonable_range = False

                # More lenient criteria
                is_valid = (
                    (valid_ratio >= min_valid_ratio)
                    and (recent_ratio >= min_recent_ratio)
                    and reasonable_range
                )

                if not is_valid:
                    logger.debug(
                        f"Rejected {ticker}: valid_ratio={valid_ratio:.2f}, recent_ratio={recent_ratio:.2f}, reasonable_range={reasonable_range}"
                    )

                valid_mask.append(is_valid)

            valid_mask = np.array(valid_mask)

            logger.info("数据清理结果:")
            logger.info(f"  原始股票: {len(prices)}")
            logger.info(f"  清理后的有效股票: {np.sum(valid_mask)}")
            logger.info(
                f"  拒绝率: {(1 - np.sum(valid_mask) / len(prices)) * 100:.1f}%"
            )

            if not np.any(valid_mask):
                logger.error("没有有效价格数据的股票")
                logger.error(
                    f"检查了 {len(prices)} 支股票，没有符合有效性标准的"
                )

                # Debug: show some sample data
                logger.error("样本数据分析:")
                for i in range(min(5, len(prices))):
                    stock_prices = prices[i]
                    ticker = self.all_stocks_data["tickers"][i]
                    valid_points = np.sum(
                        (stock_prices > 0) & np.isfinite(stock_prices)
                    )
                    valid_ratio = valid_points / len(stock_prices)
                    recent_prices = stock_prices[-10:]
                    recent_valid = np.sum(
                        (recent_prices > 0) & np.isfinite(recent_prices)
                    )
                    recent_ratio = recent_valid / len(recent_prices)

                    logger.error(
                        f"  {ticker}: {valid_ratio:.1%} valid, recent {recent_ratio:.1%}, shape {stock_prices.shape}"
                    )
                    logger.error(f"    Recent values: {recent_prices}")

                return False

            # Filter data to only valid stocks
            valid_prices = cleaned_prices[valid_mask]
            valid_tickers = [
                self.all_stocks_data["tickers"][i]
                for i in range(len(valid_mask))
                if valid_mask[i]
            ]
            valid_sectors = {
                ticker: self.all_stocks_data["sectors"][ticker]
                for ticker in valid_tickers
            }
            valid_industries = {
                ticker: self.all_stocks_data["industries"][ticker]
                for ticker in valid_tickers
            }

            logger.info(
                f"Filtered to {len(valid_tickers)} stocks with valid price data (from {len(self.all_stocks_data['tickers'])})"
            )

            # Final data cleaning for model input
            final_prices = []
            final_tickers = []
            final_sectors = {}
            final_industries = {}

            for i, ticker in enumerate(valid_tickers):
                stock_prices = valid_prices[i]

                # Ensure no NaN or invalid values remain
                if np.all(np.isfinite(stock_prices)) and np.all(stock_prices > 0):
                    final_prices.append(stock_prices)
                    final_tickers.append(ticker)
                    final_sectors[ticker] = valid_sectors[ticker]
                    final_industries[ticker] = valid_industries[ticker]
                else:
                    logger.debug(
                        f"Final rejection of {ticker}: contains NaN or invalid values"
                    )

            if len(final_prices) == 0:
                logger.error("没有股票通过最终验证")
                return False

            # Ensure minimum number of stocks for meaningful analysis
            min_stocks_required = 10  # 进一步降低最小要求以适应更多市场情况
            if len(final_prices) < min_stocks_required:
                logger.warning(
                    f"Only {len(final_prices)} stocks passed validation (minimum: {min_stocks_required})"
                )
                logger.warning(
                    "Relaxing validation criteria to ensure sufficient data..."
                )

                # Emergency fallback: use top stocks by data completeness
                stock_scores = []
                for i, ticker in enumerate(valid_tickers):
                    stock_prices = valid_prices[i]
                    valid_points = np.sum(
                        np.isfinite(stock_prices) & (stock_prices > 0)
                    )
                    completeness = valid_points / len(stock_prices)
                    stock_scores.append((completeness, ticker, i))

                # Sort by completeness and take top stocks
                stock_scores.sort(reverse=True)
                emergency_count = min(min_stocks_required, len(stock_scores))

                final_prices = []
                final_tickers = []
                final_sectors = {}
                final_industries = {}

                for completeness, ticker, i in stock_scores[:emergency_count]:
                    stock_prices = valid_prices[i]
                    # Fill remaining NaN with forward fill
                    mask = np.isnan(stock_prices)
                    if np.any(~mask):
                        # Forward fill all NaN values
                        last_valid = None
                        for j in range(len(stock_prices)):
                            if not mask[j]:
                                last_valid = stock_prices[j]
                            elif last_valid is not None:
                                stock_prices[j] = last_valid

                        # If still has NaN at the beginning, backward fill
                        for j in range(len(stock_prices) - 1, -1, -1):
                            if not np.isnan(stock_prices[j]):
                                break
                        if j < len(stock_prices) - 1:
                            stock_prices[: j + 1] = stock_prices[j]

                        # Final check
                        if np.all(np.isfinite(stock_prices)) and np.all(
                            stock_prices > 0
                        ):
                            final_prices.append(stock_prices)
                            final_tickers.append(ticker)
                            final_sectors[ticker] = valid_sectors[ticker]
                            final_industries[ticker] = valid_industries[ticker]

                logger.info(f"Emergency fallback: recovered {len(final_prices)} stocks")

            if len(final_prices) == 0:
                logger.error("即使紧急备用方案也失败了 - 没有可用数据")
                return False

            final_prices = np.array(final_prices)

            logger.info(
                f"Final dataset: {len(final_tickers)} stocks ready for analysis"
            )

            # Update the data structure
            self.all_stocks_data["price"] = final_prices
            self.all_stocks_data["tickers"] = final_tickers
            self.all_stocks_data["sectors"] = final_sectors
            self.all_stocks_data["industries"] = final_industries

            logp = np.log(final_prices)
            num_stocks, t = logp.shape

            logger.info(f"Training model with {num_stocks} stocks, {t} time periods")

            # Extract hierarchical information
            info = extract_hierarchical_info(
                self.all_stocks_data["sectors"], self.all_stocks_data["industries"]
            )

            # Verify data consistency
            logger.info("数据一致性检查:")
            logger.info(f"  价格数据中的股票: {num_stocks}")
            logger.info(f"  分层信息中的股票: {info['num_stocks']}")
            logger.info(
                f"  行业字典长度: {len(self.all_stocks_data['sectors'])}"
            )
            logger.info(
                f"  子行业字典长度: {len(self.all_stocks_data['industries'])}"
            )

            # Handle data consistency - use intersection of available data
            if info["num_stocks"] != num_stocks:
                logger.warning(
                    f"Data mismatch: price data has {num_stocks} stocks but hierarchical info has {info['num_stocks']}"
                )
                logger.info("过滤以仅使用具有完整信息的股票...")

                # Get stocks that have both price data and hierarchical info
                price_tickers = set(self.all_stocks_data["tickers"])
                sector_tickers = set(self.all_stocks_data["sectors"].keys())
                industry_tickers = set(self.all_stocks_data["industries"].keys())

                # Find intersection - stocks with complete data
                valid_tickers = price_tickers & sector_tickers & industry_tickers
                valid_tickers = list(valid_tickers)

                logger.info(f"找到 {len(valid_tickers)} 支具有完整数据的股票")

                if len(valid_tickers) < 50:
                    logger.error(
                        f"具有完整数据的股票太少: {len(valid_tickers)}"
                    )
                    return False

                # Filter all data to only include valid tickers
                ticker_indices = [
                    i
                    for i, ticker in enumerate(self.all_stocks_data["tickers"])
                    if ticker in valid_tickers
                ]

                # Update price data using final_prices
                filtered_final_prices = final_prices[ticker_indices, :]

                # Update tickers list
                filtered_tickers = [
                    self.all_stocks_data["tickers"][i] for i in ticker_indices
                ]
                self.all_stocks_data["tickers"] = filtered_tickers

                # Update sectors and industries to match filtered tickers
                filtered_sectors = {
                    ticker: self.all_stocks_data["sectors"][ticker]
                    for ticker in filtered_tickers
                    if ticker in self.all_stocks_data["sectors"]
                }
                filtered_industries = {
                    ticker: self.all_stocks_data["industries"][ticker]
                    for ticker in filtered_tickers
                    if ticker in self.all_stocks_data["industries"]
                }

                self.all_stocks_data["sectors"] = filtered_sectors
                self.all_stocks_data["industries"] = filtered_industries
                self.all_stocks_data["price"] = filtered_final_prices

                # Recalculate info with filtered data
                info = extract_hierarchical_info(filtered_sectors, filtered_industries)

                # Update counts and recalculate logp
                num_stocks = len(filtered_tickers)
                final_prices = filtered_final_prices
                logp = np.log(final_prices)
                t = logp.shape[1]  # Update time dimension

                logger.info(
                    f"Filtered dataset: {len(filtered_tickers)} stocks with complete information"
                )
                logger.info(
                    f"Final consistency check: price={final_prices.shape[0]}, hierarchical={info['num_stocks']}"
                )
                logger.info(f"Updated logp shape: {logp.shape}")
                logger.info(
                    f"Debug: valid_tickers={len(valid_tickers)}, filtered_tickers={len(filtered_tickers)}"
                )
                logger.info(
                    f"Debug: filtered_sectors={len(filtered_sectors)}, filtered_industries={len(filtered_industries)}"
                )

                # Verify final consistency
                if logp.shape[0] != info["num_stocks"]:
                    logger.error(
                        "CRITICAL: Still have dimension mismatch after filtering!"
                    )
                    logger.error(
                        f"logp shape: {logp.shape}, info stocks: {info['num_stocks']}"
                    )
                    logger.error("这表明过滤逻辑中存在错误")
                    logger.error(
                        f"Debug: valid_tickers={len(valid_tickers)}, filtered_tickers={len(filtered_tickers)}"
                    )
                    logger.error(
                        f"Debug: filtered_sectors={len(filtered_sectors)}, filtered_industries={len(filtered_industries)}"
                    )
                    return False

                logger.info("✅ 数据过滤成功 - 所有维度都匹配")

            # Prepare time features
            order = min(52, t // 4)  # Adaptive order based on data length
            info["tt"] = (
                np.linspace(1 / t, 1, t) ** np.arange(order + 1).reshape(-1, 1)
            ).astype("float32")
            info["order_scale"] = np.ones((1, order + 1), dtype="float32")

            # Train the model
            logger.info("训练MSIS-MCS模型...")
            logger.info(
                f"最终训练数据: logp维度={logp.shape}, 信息股票={info['num_stocks']}"
            )
            phi_m, psi_m, phi_s, psi_s, phi_i, psi_i, phi, psi = train_msis_mcs(
                logp, info, num_steps=10000
            )

            # Generate predictions
            horizon = 5  # 5-day prediction horizon
            tt_pred = (
                (1 + (np.arange(1 + horizon) / t))
                ** np.arange(order + 1).reshape(-1, 1)
            ).astype("float32")

            # Get price predictions and statistics
            logp_pred, std_logp_pred = estimate_logprice_statistics(phi, psi, tt_pred)
            price_pred, std_price_pred = estimate_price_statistics(
                logp_pred, std_logp_pred
            )

            # Calculate current vs predicted scores
            current_logp = logp[:, -1]  # Latest log prices
            predicted_logp = logp_pred[:, horizon]  # 5-day ahead predictions

            # Calculate trend scores (positive = above trend, negative = below trend)
            # Use the standard deviation for the same horizon as the prediction
            std_at_horizon = (
                std_logp_pred[:, horizon]
                if std_logp_pred.ndim > 1
                else std_logp_pred.squeeze()
            )
            trend_scores = (predicted_logp - current_logp) / std_at_horizon

            # Rate stocks based on trend with configurable extreme standards
            # Positive scores = above trend (overvalued), Negative scores = below trend (undervalued)
            # Analyze trend score distribution first
            self._analyze_trend_score_distribution(trend_scores)

            # 保存当前趋势分数用于动态阈值计算
            self.current_trend_scores = trend_scores

            # Use configurable thresholds to find extreme stocks (preserving original design intent)
            extreme_bounds = self._get_rating_bounds(self.extreme_level)
            ratings = self._rate_stocks_corrected(trend_scores, extreme_bounds)

            logger.info(
                f"Using '{self.extreme_level}' extreme level with bounds: {extreme_bounds}"
            )

            # Generate ML correlation matches for options trading
            logger.info("🔍 为期权交易生成ML相关性匹配...")
            try:
                from core.stats import estimate_matches

                matches = estimate_matches(
                    self.all_stocks_data["tickers"], phi.numpy(), info["tt"]
                )
                logger.info(f"✅ 生成了 {len(matches)} 个ML相关性匹配")
            except Exception as e:
                logger.warning(f"生成ML匹配失败: {e}")
                matches = {}

            # Store model results
            self.model_results = {
                "tickers": self.all_stocks_data["tickers"],
                "current_prices": np.exp(current_logp),
                "predicted_prices": np.exp(predicted_logp),
                "trend_scores": trend_scores,
                "ratings": ratings,
                "sectors": self.all_stocks_data["sectors"],
                "industries": self.all_stocks_data["industries"],
                "matches": matches,  # Add ML correlation matches for options trading
                "price_data": {  # Add price data for options analysis
                    ticker: self.all_stocks_data["price"][i]
                    for i, ticker in enumerate(self.all_stocks_data["tickers"])
                },
                "model_params": {
                    "phi": phi.numpy(),
                    "psi": psi.numpy(),
                    "phi_m": phi_m.numpy(),
                    "psi_m": psi_m.numpy(),
                    "phi_s": phi_s.numpy(),
                    "psi_s": psi_s.numpy(),
                    "phi_i": phi_i.numpy(),
                    "psi_i": psi_i.numpy(),
                },
            }

            # Cache the model results
            with open(self.model_cache_file, "wb") as f:
                pickle.dump(self.model_results, f)

            logger.info("模型分析成功完成")
            logger.info(f"趋势之上股票: {sum(1 for r in ratings if 'ABOVE' in r)}")
            logger.info(f"趋势之下股票: {sum(1 for r in ratings if 'BELOW' in r)}")
            logger.info(f"沿着趋势股票: {sum(1 for r in ratings if 'ALONG' in r)}")

            return True

    def _rate_stocks_corrected(self, scores: np.ndarray, bounds: dict) -> list:
        """7级评级函数，包含极端分类"""
        ratings = []
        for score in scores:
            if score < bounds["EXTREME BELOW TREND"]:
                ratings.append("EXTREME BELOW TREND")
            elif score < bounds["HIGHLY BELOW TREND"]:
                ratings.append("HIGHLY BELOW TREND")
            elif score < bounds["BELOW TREND"]:
                ratings.append("BELOW TREND")
            elif score <= bounds["ALONG TREND"]:
                ratings.append("ALONG TREND")
            elif score <= bounds["ABOVE TREND"]:
                ratings.append("ABOVE TREND")
            elif score <= bounds["HIGHLY ABOVE TREND"]:
                ratings.append("HIGHLY ABOVE TREND")
            else:
                ratings.append("EXTREME ABOVE TREND")
        return ratings

    def _analyze_trend_score_distribution(self, trend_scores: np.ndarray):
        """分析趋势分数的分布以优化边界"""
        import numpy as np

        # Calculate percentiles
        percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
        values = np.percentile(trend_scores, percentiles)

        logger.info("📊 趋势分数分布分析:")
        logger.info(
            f"  Min: {np.min(trend_scores):.3f}, Max: {np.max(trend_scores):.3f}"
        )
        logger.info(
            f"  Mean: {np.mean(trend_scores):.3f}, Std: {np.std(trend_scores):.3f}"
        )

        for p, v in zip(percentiles, values):
            logger.info(f"  第{p:2d}百分位: {v:6.3f}")

        # Count stocks in different ranges
        ranges = [
            ("< -5.0", np.sum(trend_scores < -5.0)),
            ("-5.0 to -4.0", np.sum((trend_scores >= -5.0) & (trend_scores < -4.0))),
            ("-4.0 to -3.0", np.sum((trend_scores >= -4.0) & (trend_scores < -3.0))),
            ("-3.0 to -2.5", np.sum((trend_scores >= -3.0) & (trend_scores < -2.5))),
            ("-2.5 to -2.0", np.sum((trend_scores >= -2.5) & (trend_scores < -2.0))),
            ("-2.0 to -1.0", np.sum((trend_scores >= -2.0) & (trend_scores < -1.0))),
            ("-1.0 to 0.0", np.sum((trend_scores >= -1.0) & (trend_scores < 0.0))),
            ("0.0 to 1.0", np.sum((trend_scores >= 0.0) & (trend_scores < 1.0))),
            ("1.0 to 2.0", np.sum((trend_scores >= 1.0) & (trend_scores < 2.0))),
            ("2.0 to 2.5", np.sum((trend_scores >= 2.0) & (trend_scores < 2.5))),
            ("2.5 to 3.0", np.sum((trend_scores >= 2.5) & (trend_scores < 3.0))),
            ("3.0 to 4.0", np.sum((trend_scores >= 3.0) & (trend_scores < 4.0))),
            ("4.0 to 5.0", np.sum((trend_scores >= 4.0) & (trend_scores < 5.0))),
            ("> 5.0", np.sum(trend_scores >= 5.0)),
        ]

        logger.info("📈 各区间股票数量:")
        for range_name, count in ranges:
            percentage = (count / len(trend_scores)) * 100
            logger.info(f"  {range_name:12s}: {count:4d} 支股票 ({percentage:5.1f}%)")

    def _get_rating_bounds(self, extreme_level: str = "high") -> dict:
        """基于实际数据分布获取评级边界 - 动态校准"""
        # 动态计算阈值，基于当前数据的实际分布
        if hasattr(self, "current_trend_scores") and len(self.current_trend_scores) > 0:
            scores = np.array(self.current_trend_scores)
            p1 = np.percentile(scores, 1)  # 1st percentile
            p5 = np.percentile(scores, 5)  # 5th percentile
            p10 = np.percentile(scores, 10)  # 10th percentile
            p90 = np.percentile(scores, 90)  # 90th percentile
            p95 = np.percentile(scores, 95)  # 95th percentile
            p99 = np.percentile(scores, 99)  # 99th percentile

            logger.info("📊 基于当前数据的动态阈值:")
            logger.info(f"   第1百分位: {p1:.1f}, 第5位: {p5:.1f}, 第10位: {p10:.1f}")
            logger.info(
                f"   第90百分位: {p90:.1f}, 第95位: {p95:.1f}, 第99位: {p99:.1f}"
            )
        else:
            # 回退到静态阈值
            p1, p5, p10 = -23.0, -10.0, -5.0
            p90, p95, p99 = 29.0, 37.0, 42.0

        if extreme_level == "high":
            # High extreme standards - truly ultra-rare boundaries
            return {
                "EXTREME BELOW TREND": p1 - 5.0,  # 比1st percentile更低
                "HIGHLY BELOW TREND": p1,  # 1st percentile
                "BELOW TREND": p5,  # 5th percentile
                "ALONG TREND": p90,  # Normal range (5th to 90th)
                "ABOVE TREND": p95,  # 95th percentile
                "HIGHLY ABOVE TREND": p99,  # 99th percentile
                "EXTREME ABOVE TREND": float("inf"),  # >99th percentile
            }
        elif extreme_level == "medium":
            # Medium extreme standards - rare but accessible
            return {
                "EXTREME BELOW TREND": p5,  # 5th percentile
                "HIGHLY BELOW TREND": p10,  # 10th percentile
                "BELOW TREND": p10 + 2.0,  # 略高于10th percentile
                "ALONG TREND": p90,  # Normal range
                "ABOVE TREND": p90 + 2.0,  # 略高于90th percentile
                "HIGHLY ABOVE TREND": p95,  # 95th percentile
                "EXTREME ABOVE TREND": float("inf"),  # >95th percentile
            }
        else:  # "low"
            # Low extreme standards - more opportunities
            return {
                "EXTREME BELOW TREND": -23.0,  # ~1st percentile
                "HIGHLY BELOW TREND": -10.0,  # ~5th percentile
                "BELOW TREND": -3.0,  # ~12th percentile
                "ALONG TREND": 25.0,  # Normal range (-3 to 25)
                "ABOVE TREND": 32.0,  # ~95th percentile
                "HIGHLY ABOVE TREND": 37.0,  # ~99th percentile
                "EXTREME ABOVE TREND": float("inf"),  # >99th percentile
            }

    def step3_correlation_analysis(self) -> bool:
        """步骤3: 执行相关性分析以识别交易机会"""
        logger.info("步骤 3: 执行相关性分析...")

        try:
            if not self.model_results:
                logger.error("没有可用于相关性分析的模型结果")
                return False

            # Get stocks that are significantly above or below trend
            tickers = self.model_results["tickers"]
            ratings = self.model_results["ratings"]
            trend_scores = self.model_results["trend_scores"]
            sectors = self.model_results["sectors"]

            # Identify candidate stocks across all 7 trend categories
            extreme_below_trend = []
            highly_below_trend = []
            below_trend = []
            along_trend = []
            above_trend = []
            highly_above_trend = []
            extreme_above_trend = []

            for i, (ticker, rating, score) in enumerate(
                zip(tickers, ratings, trend_scores)
            ):
                stock_info = {
                    "ticker": ticker,
                    "score": score,
                    "sector": sectors[ticker],
                    "current_price": self.model_results["current_prices"][i],
                    "predicted_price": self.model_results["predicted_prices"][i],
                }

                if "EXTREME BELOW" in rating:
                    extreme_below_trend.append(stock_info)
                elif "HIGHLY BELOW" in rating:
                    highly_below_trend.append(stock_info)
                elif (
                    "BELOW" in rating
                    and "HIGHLY" not in rating
                    and "EXTREME" not in rating
                ):
                    below_trend.append(stock_info)
                elif "ALONG" in rating:
                    along_trend.append(stock_info)
                elif (
                    "ABOVE" in rating
                    and "HIGHLY" not in rating
                    and "EXTREME" not in rating
                ):
                    above_trend.append(stock_info)
                elif "HIGHLY ABOVE" in rating:
                    highly_above_trend.append(stock_info)
                elif "EXTREME ABOVE" in rating:
                    extreme_above_trend.append(stock_info)

            # Perform correlation analysis within sectors
            correlation_opportunities = []

            # Group by sector for correlation analysis (include extreme categories)
            sector_groups = {}
            for stock in (
                extreme_below_trend
                + highly_below_trend
                + highly_above_trend
                + extreme_above_trend
            ):
                sector = stock["sector"]
                if sector not in sector_groups:
                    sector_groups[sector] = []
                sector_groups[sector].append(stock)

            # Find correlation opportunities within each sector
            for sector, stocks in sector_groups.items():
                if len(stocks) >= 2:
                    # Sort by trend score
                    stocks.sort(key=lambda x: x["score"])

                    # Look for pairs with opposite trends for potential correlation trades
                    sector_below_trend = [
                        s for s in stocks if s["score"] < -1.0
                    ]  # Undervalued (buy candidates)
                    sector_above_trend = [
                        s for s in stocks if s["score"] > 1.0
                    ]  # Overvalued (sell/avoid candidates)

                    if sector_below_trend and sector_above_trend:
                        correlation_opportunities.append(
                            {
                                "sector": sector,
                                "buy_candidates": sector_below_trend,  # Buy undervalued
                                "sell_candidates": sector_above_trend,  # Sell overvalued
                                "correlation_strength": len(sector_below_trend)
                                + len(sector_above_trend),
                            }
                        )

            self.correlation_analysis = {
                "extreme_below_trend": extreme_below_trend,
                "highly_below_trend": highly_below_trend,
                "below_trend": below_trend,
                "along_trend": along_trend,
                "above_trend": above_trend,
                "highly_above_trend": highly_above_trend,
                "extreme_above_trend": extreme_above_trend,
                "correlation_opportunities": correlation_opportunities,
                "sector_groups": sector_groups,
            }

            logger.info(f"发现 {len(extreme_below_trend)} 支极度低于趋势的股票")
            logger.info(f"发现 {len(highly_below_trend)} 支高度低于趋势的股票")
            logger.info(f"发现 {len(below_trend)} 支低于趋势的股票")
            logger.info(f"发现 {len(along_trend)} 支沿着趋势的股票")
            logger.info(f"发现 {len(above_trend)} 支高于趋势的股票")
            logger.info(f"发现 {len(highly_above_trend)} 支高度高于趋势的股票")
            logger.info(f"发现 {len(extreme_above_trend)} 支极度高于趋势的股票")
            logger.info(f"识别到 {len(correlation_opportunities)} 个相关性机会")

            return True

        except Exception as e:
            logger.error(f"步骤 3 出错: {e}")
            return False

    def step4_generate_trading_signals(self) -> dict:
        """步骤4: 基于相关性分析生成交易信号"""
        logger.info("步骤 4: 生成交易信号...")
        
        if not self.correlation_analysis:
            logger.error("没有可用于信号生成的相关性分析")
            return {}
        
        # Generate standardized trading signals
        trading_signals = {
            "extreme_above_trend": self.correlation_analysis.get("extreme_above_trend", []),
            "highly_above_trend": self.correlation_analysis.get("highly_above_trend", []),
            "extreme_below_trend": self.correlation_analysis.get("extreme_below_trend", []),
            "highly_below_trend": self.correlation_analysis.get("highly_below_trend", []),
            "along_trend": self.correlation_analysis.get("along_trend", []),
            "timestamp": datetime.now(),
            "market_summary": {
                "total_stocks_analyzed": sum(len(stocks) for stocks in self.correlation_analysis.values() if isinstance(stocks, list)),
                "extreme_opportunities": len(self.correlation_analysis.get("extreme_above_trend", [])) + len(self.correlation_analysis.get("extreme_below_trend", [])),
                "high_confidence_signals": len(self.correlation_analysis.get("highly_above_trend", [])) + len(self.correlation_analysis.get("highly_below_trend", []))
            }
        }
        
        logger.info(f"生成交易信号完成:")
        logger.info(f"  极端上涨机会: {len(trading_signals['extreme_above_trend'])}")
        logger.info(f"  高度上涨机会: {len(trading_signals['highly_above_trend'])}")
        logger.info(f"  极端下跌机会: {len(trading_signals['extreme_below_trend'])}")
        logger.info(f"  高度下跌机会: {len(trading_signals['highly_below_trend'])}")
        logger.info(f"  趋势稳定机会: {len(trading_signals['along_trend'])}")
        
        return trading_signals

    async def step4_prepare_trading_signals(self) -> dict:
        """步骤4: 准备标准化市场交易信号"""
        logger.info("步骤 4: 准备标准化市场交易信号...")

        try:
            if not self.correlation_analysis:
                logger.error("没有可用于交易的相关性分析")
                return {}

            # 生成标准化的交易信号
            trading_signals = self.step4_generate_trading_signals()
            if not trading_signals:
                logger.warning("未生成任何交易信号")
                return {}

            logger.info(f"📊 市场摘要:")
            logger.info(f"  - 总分析股票数: {trading_signals['market_summary']['total_stocks_analyzed']}")
            logger.info(f"  - 极端机会数量: {trading_signals['market_summary']['extreme_opportunities']}")
            logger.info(f"  - 高置信度信号: {trading_signals['market_summary']['high_confidence_signals']}")

            # 存储交易信号供调用方使用
            self.trading_signals = trading_signals

            logger.info("✅ 交易信号准备完成，等待调用方决定如何使用")
            return trading_signals

        except Exception as e:
            logger.error(f"步骤 4 出错: {e}")
            return {}

    async def execute_trading_with_bot(self, trading_signals: dict = None, bot_instance=None) -> bool:
        """
        使用指定的机器人执行交易

        Args:
            trading_signals: 交易信号字典，如果为None则使用self.trading_signals
            bot_instance: 机器人实例，如果为None则使用self.bot

        Returns:
            bool: 交易执行是否成功
        """
        if trading_signals is None:
            trading_signals = getattr(self, 'trading_signals', {})

        if not trading_signals:
            logger.warning("没有可用的交易信号")
            return False

        if bot_instance is None:
            bot_instance = self.bot

        try:
            bot_name = bot_instance.__class__.__name__
            logger.info(f"🤖 {bot_name} 机器人开始分析市场信号...")

            # 调用机器人的分析交易接口
            trading_decisions = await bot_instance.analyze_and_trade(trading_signals)

            if trading_decisions:
                logger.info(f"✅ {bot_name} 机器人完成交易决策")

                # 更新投资组合价值
                if 'current_prices' in trading_decisions:
                    bot_instance.compute_capital(trading_decisions['current_prices'])

                # 存储交易决策
                self.trading_decisions = {
                    "timestamp": datetime.now(),
                    "bot_decisions": trading_decisions,
                    "portfolio_value": bot_instance.capital,
                    "cash": bot_instance.uninvested,
                    "invested": bot_instance.invested,
                    "positions": dict(bot_instance.portfolio),
                }

                logger.info("📊 交易执行完成:")
                logger.info(f"  - 投资组合价值: ${bot_instance.capital:.2f}")
                logger.info(f"  - 现金: ${bot_instance.uninvested:.2f}")
                logger.info(f"  - 已投资: ${bot_instance.invested:.2f}")
                logger.info(f"  - 持仓数量: {len(bot_instance.portfolio)}")
            else:
                logger.info(f"📋 {bot_name} 机器人决定本次不进行交易")

            return True

        except Exception as e:
            logger.error(f"交易执行出错: {e}")
            return False





    @async_handle_errors(default_return=None, error_category=ErrorCategory.SYSTEM)
    async def _generate_stock_correlation_charts(self, trading_info: dict):
        """生成股票相关性图表"""
        try:
            if not trading_info:
                logger.warning("没有交易信息，跳过图表生成")
                return

            logger.info(f"📊 为 {len(trading_info)} 支股票生成相关性图表...")

            # 确保图表目录存在
            os.makedirs(self.charts_dir, exist_ok=True)

            # 导入图表生成函数
            from core.charts import plot_stock_estimates

            # 如果有模型结果和股票数据，生成图表
            if hasattr(self, 'all_stocks_data') and self.all_stocks_data:
                # 获取交易股票的索引
                traded_tickers = list(trading_info.keys())
                all_tickers = self.all_stocks_data.get('tickers', [])

                # 找到交易股票在数据中的索引
                traded_indices = []
                for ticker in traded_tickers:
                    try:
                        idx = all_tickers.index(ticker)
                        traded_indices.append(idx)
                    except ValueError:
                        logger.warning(f"未找到 {ticker} 的数据索引")

                if traded_indices and hasattr(self, 'current_trend_scores'):
                    # 获取交易股票的价格数据并确保形状一致
                    traded_prices = []
                    traded_volumes = []
                    min_length = float('inf')

                    # 首先找到最短的时间序列长度
                    for i in traded_indices:
                        price_data = self.all_stocks_data['price'][i]
                        volume_data = self.all_stocks_data['volume'][i]
                        if len(price_data) < min_length:
                            min_length = len(price_data)

                    # 截断所有数据到相同长度
                    for i in traded_indices:
                        price_data = self.all_stocks_data['price'][i][-min_length:]
                        volume_data = self.all_stocks_data['volume'][i][-min_length:]
                        traded_prices.append(price_data)
                        traded_volumes.append(volume_data)

                    # 创建用于图表的数据子集
                    chart_data = {
                        'tickers': [all_tickers[i] for i in traded_indices],
                        'price': np.array(traded_prices),
                        'volume': np.array(traded_volumes),
                        'currencies': [self.all_stocks_data['currencies'][i] for i in traded_indices],
                        'dates': self.all_stocks_data['dates'][-min_length:]
                    }

                    # 获取对应的趋势分数
                    trend_scores = np.array([self.current_trend_scores[i] for i in traded_indices])

                    # 创建估计值和标准差（简化版本）
                    num_stocks, num_periods = chart_data['price'].shape
                    est = np.zeros((num_stocks, num_periods))
                    std = np.ones((num_stocks, num_periods)) * 0.1

                    # 创建排名信息
                    rank = list(range(len(traded_indices)))
                    ranked_rates = trend_scores

                    # 直接生成图表到正确的目录
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    new_filename = f"stock_correlation_analysis_{timestamp}.png"
                    target_path = os.path.join(self.charts_dir, new_filename)

                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)

                    # 生成图表，直接保存到目标目录
                    plot_stock_estimates(
                        chart_data, est, std, "rate", rank, ranked_rates,
                        output_dir=os.path.dirname(target_path)
                    )

                    # 重命名文件到最终名称
                    generated_file = os.path.join(os.path.dirname(target_path), "stock_estimation.png")
                    if os.path.exists(generated_file):
                        os.rename(generated_file, target_path)
                        logger.info(f"📊 股票相关性图表已保存: {target_path}")
                    else:
                        logger.warning("图表生成失败 - 未找到生成的文件")
                else:
                    logger.warning("无法生成图表 - 缺少必要的数据或趋势分数")
            else:
                logger.warning("无法生成图表 - 缺少股票数据")

        except Exception as e:
            logger.error(f"生成股票相关性图表时出错: {e}")
            import traceback
            logger.debug(f"详细错误信息: {traceback.format_exc()}")
        finally:
            # Disconnect if connected
            if self.ibkr_client.connected:
                self.ibkr_client.disconnect()

            # Also disconnect bot's IBKR client if connected
            if hasattr(self.bot, "disconnect_from_ibkr"):
                self.bot.disconnect_from_ibkr()

    async def step5_options_arbitrage(self) -> bool:
        """步骤5: 基于相关性背离执行期权套利"""
        logger.info("🚀 步骤 5: 分析期权套利机会...")
        logger.info(
            f"📊 可用数据: 模型结果={bool(self.model_results)}, 相关性分析={bool(self.correlation_analysis)}"
        )

        try:
            if not self.model_results or not self.correlation_analysis:
                logger.error(
                    "❌ 没有可用于期权套利的模型结果或相关性分析"
                )
                return False

            # Log correlation analysis structure
            if self.correlation_analysis:
                logger.info(
                    f"📈 相关性分析类别: {list(self.correlation_analysis.keys())}"
                )
                for category, stocks in self.correlation_analysis.items():
                    if isinstance(stocks, list):
                        logger.info(f"   {category}: {len(stocks)} 支股票")

            # Import options arbitrage system
            logger.info("🔧 导入期权套利系统...")
            from options.arbitrage import OptionsArbitrageSystem

            # Initialize options arbitrage system
            logger.info("⚙️ 初始化期权套利系统...")
            options_system = OptionsArbitrageSystem(
                self.config, self.options_charts_dir
            )

            # Calculate capital allocation - 增加期权资金分配以支持交易
            options_capital = self.bot.capital * 0.3  # Use 30% of capital for options
            logger.info(
                f"💰 期权资金分配: ${options_capital:,.2f} (${self.bot.capital:,.2f}的30%)"
            )

            # Run options arbitrage analysis
            logger.info("🎯 开始期权套利分析...")
            success = await options_system.run_options_arbitrage(
                self.correlation_analysis, self.model_results, options_capital
            )

            if success:
                logger.info("✅ 期权套利分析成功完成")
            else:
                logger.warning("⚠️ 期权套利分析完成但存在问题")

            return success

        except Exception as e:
            logger.error(f"❌ 步骤 5 (期权套利) 出错: {e}")
            import traceback

            logger.error(f"📋 跟踪: {traceback.format_exc()}")
            return False

    async def step6_manage_portfolio_risk(self) -> bool:
        """步骤6: 使用智能止损管理投资组合风险"""
        logger.info("步骤 6: 使用智能止损管理投资组合风险...")

        try:
            if not self.model_results or not self.correlation_analysis:
                logger.error(
                    "没有可用于风险管理的模型结果或相关性分析"
                )
                return False

            # Initialize portfolio manager if not already done
            if not self.portfolio_manager:
                self.portfolio_manager = PortfolioManager(self.bot, self.config.ibkr)

            # Prepare market data for risk management
            market_data = self._prepare_market_data_for_risk_management()

            if not market_data:
                logger.info("没有可用于风险管理的市场数据")
                return True

            # Execute risk management cycle
            risk_report = await self.portfolio_manager.execute_risk_management_cycle(
                market_data, self.correlation_analysis
            )

            if risk_report.get("status") == "success":
                logger.info("✅ 投资组合风险管理成功完成")

                # Log key metrics
                metrics = risk_report.get("portfolio_metrics", {})
                if metrics:
                    logger.info(
                        f"📊 投资组合价值: ${metrics.get('total_value', 0):,.2f}"
                    )
                    logger.info(
                        f"💰 总盈亏: ${metrics.get('total_pnl', 0):,.2f} ({metrics.get('total_pnl_pct', 0):.1f}%)"
                    )
                    logger.info(
                        f"🛡️ 风险级别: {risk_report.get('risk_level', 'Unknown')}"
                    )
                    logger.info(
                        f"📋 止损订单: {risk_report.get('stop_loss_orders', 0)}"
                    )

                # Log recommendations
                recommendations = risk_report.get("recommendations", [])
                if recommendations:
                    logger.info("💡 风险管理建议:")
                    for rec in recommendations:
                        logger.info(f"  {rec}")

                return True
            else:
                logger.warning(
                    f"风险管理完成，状态: {risk_report.get('status')}"
                )
                return True

        except Exception as e:
            logger.error(f"步骤 6 出错: {e}")
            return False

    def _prepare_market_data_for_risk_management(self) -> Dict:
        """准备投资组合管理器期望格式的市场数据"""
        if not self.model_results:
            return {}

        market_data = {}

        for i, ticker in enumerate(self.model_results["tickers"]):
            market_data[ticker] = {
                "current_price": self.model_results["current_prices"][i],
                "predicted_price": self.model_results["predicted_prices"][i],
                "trend_score": self.model_results["trend_scores"][i],
                "sector": self.model_results["sectors"].get(ticker, "Unknown"),
                "industry": self.model_results["industries"].get(ticker, "Unknown"),
                "volatility": self._estimate_volatility(ticker, i),
            }

        return market_data

    def _get_stock_rating(self, ticker: str) -> str:
        """获取特定股票的评级"""
        # Search through all 7 rating categories
        for rating, stocks in [
            (
                "EXTREME BELOW TREND",
                self.correlation_analysis.get("extreme_below_trend", []),
            ),
            (
                "HIGHLY BELOW TREND",
                self.correlation_analysis.get("highly_below_trend", []),
            ),
            ("BELOW TREND", self.correlation_analysis.get("below_trend", [])),
            ("ALONG TREND", self.correlation_analysis.get("along_trend", [])),
            ("ABOVE TREND", self.correlation_analysis.get("above_trend", [])),
            (
                "HIGHLY ABOVE TREND",
                self.correlation_analysis.get("highly_above_trend", []),
            ),
            (
                "EXTREME ABOVE TREND",
                self.correlation_analysis.get("extreme_above_trend", []),
            ),
        ]:
            for stock in stocks:
                if stock["ticker"] == ticker:
                    return rating
        return "UNKNOWN"

    def _get_stock_rating_from_score(self, score: float) -> str:
        """使用当前边界基于趋势分数获取评级"""
        bounds = self._get_rating_bounds(self.extreme_level)

        if score < bounds["EXTREME BELOW TREND"]:
            return "EXTREME BELOW TREND"
        elif score < bounds["HIGHLY BELOW TREND"]:
            return "HIGHLY BELOW TREND"
        elif score < bounds["BELOW TREND"]:
            return "BELOW TREND"
        elif score <= bounds["ALONG TREND"]:
            return "ALONG TREND"
        elif score <= bounds["ABOVE TREND"]:
            return "ABOVE TREND"
        elif score <= bounds["HIGHLY ABOVE TREND"]:
            return "HIGHLY ABOVE TREND"
        else:
            return "EXTREME ABOVE TREND"

    def _estimate_volatility(self, ticker: str, index: int) -> float:
        """估算股票波动性 (简化计算)"""
        try:
            if self.all_stocks_data and "price" in self.all_stocks_data:
                prices = self.all_stocks_data["price"][index]
                if len(prices) > 20:
                    # Calculate 20-day volatility
                    returns = np.diff(np.log(prices[-20:]))
                    volatility = np.std(returns) * np.sqrt(252)  # Annualized
                    return float(volatility)
        except Exception as e:
            logger.debug(f"Failed to calculate volatility for {ticker}: {e}")

        # Default volatility if calculation fails
        return 0.25

    def _load_stock_info_from_csv(self):
        """从stock_info.csv加载股票分类信息"""
        stock_info = {}
        # 使用统一配置中的文件路径
        stock_info_file = DEFAULT_CONFIG.system.stock_info_file
        try:
            import pandas as pd

            if os.path.exists(stock_info_file):
                df = pd.read_csv(stock_info_file)
                for _, row in df.iterrows():
                    symbol = row["symbol"]
                    stock_info[symbol] = {
                        "sector": row.get("sector", "Unknown"),
                        "industry": row.get("industry", "Unknown"),
                        "subcategory": row.get("subcategory", "Unknown"),
                        "long_name": row.get("long_name", symbol),
                    }
                logger.info(
                    f"从 {stock_info_file} 加载了 {len(stock_info)} 支股票的分类信息"
                )
            else:
                logger.warning(
                    f"未找到 {stock_info_file}，使用默认分类"
                )
        except Exception as e:
            logger.warning(f"加载 {stock_info_file} 失败: {e}")
        return stock_info

    @handle_errors(default_return=None, error_category=ErrorCategory.DATA)
    def save_daily_results(self):
        """将日常结果保存到CSV文件"""
        if not self.model_results:
            return

        # Ensure results directory exists
        results_dir = os.path.dirname(self.results_file)
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)
            logger.info(f"📁 Created results directory: {results_dir}")

        # Load stock classification info from CSV
        stock_info = self._load_stock_info_from_csv()

        # Prepare results DataFrame
        results_data = []

        for i, ticker in enumerate(self.model_results["tickers"]):
            # Get classification from CSV if available, otherwise use model data
            if ticker in stock_info:
                sector = stock_info[ticker]["sector"]
                industry = stock_info[ticker]["industry"]
            else:
                sector = self.model_results["sectors"].get(ticker, "Unknown")
                industry = self.model_results["industries"].get(ticker, "Unknown")

            results_data.append(
                    {
                        "date": datetime.now().strftime("%Y-%m-%d"),
                        "ticker": ticker,
                        "current_price": self.model_results["current_prices"][i],
                        "predicted_price": self.model_results["predicted_prices"][i],
                        "trend_score": self.model_results["trend_scores"][i],
                        "rating": self.model_results["ratings"][i],
                        "sector": sector,
                        "industry": industry,
                    }
                )

            df = pd.DataFrame(results_data)
            df.to_csv(self.results_file, index=False)

            logger.info(f"📊 Daily results saved to {self.results_file}")

    async def run_daily_analysis(self) -> bool:
        """运行完整的日常分析工作流"""
        logger.info("🚀 开始每日交易分析工作流...")
        logger.info(
            f"🔧 系统配置: 启用期权={getattr(self, 'enable_options', False)}"
        )
        logger.info("=" * 60)

        # Step 1: 强制下载最新股票数据（确保数据新鲜度）
        if not await self.step1_download_all_stocks():
            logger.error("步骤 1 失败 - 数据下载失败")
            return False

        # Step 2: Run model analysis
        if not self.step2_run_model_analysis():
            logger.error("步骤 2 失败 - 中止工作流")
            return False

        # Step 3: Correlation analysis
        if not self.step3_correlation_analysis():
            logger.error("步骤 3 失败 - 中止工作流")
            return False

        # Step 4: Prepare trading signals
        trading_signals = await self.step4_prepare_trading_signals()
        if not trading_signals:
            logger.error("步骤 4 失败 - 中止工作流")
            return False

        # Step 4.5: Execute trading with bot (optional, can be called separately)
        if hasattr(self, 'bot') and self.bot:
            logger.info("🤖 执行机器人交易...")
            if not await self.execute_trading_with_bot(trading_signals):
                logger.warning("机器人交易执行失败，但继续工作流")
        else:
            logger.info("📋 交易信号已准备完成，等待外部调用方执行交易")

        # Step 5: Options arbitrage (if enabled)
        logger.info(
            f"🔍 检查期权套利: 启用期权={getattr(self, 'enable_options', False)}"
        )
        if hasattr(self, "enable_options") and self.enable_options:
            logger.info("✅ 期权套利已启用 - 开始步骤 5")
            if not await self.step5_options_arbitrage():
                logger.warning("步骤 5 (期权套利) 失败 - 继续执行")
        else:
            logger.info("❌ 期权套利已禁用 - 跳过步骤 5")

        # Step 6: Manage portfolio risk with smart stop losses
        if not await self.step6_manage_portfolio_risk():
            logger.warning("步骤 6 失败 - 在没有风险管理的情况下继续")
            # Don't abort workflow for risk management failure

        # Save results
        self.save_daily_results()

        logger.info("=" * 60)
        logger.info("每日交易分析成功完成!")

        return True


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Daily Trading System")
    parser.add_argument(
        "--bot",
        choices=["Adam", "Betty", "Chris", "Dany", "Eddy", "Flora"],
        default="Adam",
        help="Trading bot to use",
    )
    parser.add_argument(
        "--capital", type=float, default=10000.0, help="Initial capital"
    )
    parser.add_argument("--paper", action="store_true", help="Use paper trading")
    parser.add_argument(
        "--live", action="store_true", help="Use live trading (DANGEROUS!)"
    )
    parser.add_argument(
        "--extreme-level",
        type=str,
        default="high",
        choices=["high", "medium", "low"],
        help="Extreme threshold level: high (1-5%%), medium (5-15%%), low (10-30%%)",
    )
    parser.add_argument(
        "--clean-cache",
        action="store_true",
        help="Clean cache files for fresh download (fixes slow concurrent downloads)",
    )
    parser.add_argument(
        "--resume",
        action="store_true",
        help="Resume interrupted download from last checkpoint",
    )

    parser.add_argument(
        "--enable-options",
        action="store_true",
        help="Enable options arbitrage trading based on correlation divergence",
    )

    args = parser.parse_args()

    # Select bot class
    bot_classes = {
        "Adam": Adam,
        "Betty": Betty,
        "Chris": Chris,
        "Dany": Dany,
        "Eddy": Eddy,
        "Flora": Flora,
    }
    bot_class = bot_classes[args.bot]

    # Configure trading mode
    config = IBKRConfig()

    if args.live:
        # Live trading mode - uses real money
        config.port = 7496  # Live trading port
        print("⚠️  LIVE TRADING MODE - REAL MONEY AT RISK!")
        print("⚠️  This will execute real trades with your actual funds!")
        print("⚠️  Make sure you understand the risks before proceeding!")
        confirm = input("Type 'YES' to confirm live trading: ").strip()
        if confirm != "YES":
            print(f"Live trading cancelled (you entered: '{confirm}')")
            print("注意：必须输入准确的'YES'（全大写）以确认")
            return
        print("🔴 LIVE TRADING MODE CONFIRMED - USING REAL MONEY!")

    elif args.paper:
        # Paper trading mode - uses IBKR paper trading
        config.port = 7497  # Paper trading port
        print("📝 Paper trading mode (IBKR simulation)")

    else:
        # Default to paper trading for safety
        config.port = 7497  # Paper trading port
        print("📝 Default paper trading mode (use --paper or --live to specify)")
        print("💡 Use --paper for IBKR paper trading or --live for real trading")

    # Create and run daily trading system
    system = DailyTradingSystem(
        config,
        bot_class,
        args.capital,
        args.extreme_level,
        args.enable_options,
    )

    # Clean cache if requested (fixes slow concurrent downloads)
    if args.clean_cache:
        system.clean_cache_files()
        print("🚀 Cache cleaned - system will now use optimized sequential download")

    # Handle resume functionality
    if args.resume:
        print("🔄 Resume mode - will continue from last checkpoint")

    success = await system.run_daily_analysis()

    if success:
        print("✅ 每日分析成功完成!")
    else:
        print("❌ 每日分析失败!")


if __name__ == "__main__":
    asyncio.run(main())
