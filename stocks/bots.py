#!/usr/bin/env python3
"""
交易机器人模块
每个机器人只负责独立的交易策略，避免功能重复
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional
from config import DEFAULT_CONFIG, IBKRConfig

# 检查IBKR可用性
try:
    from ibkr.client import get_ibkr_client
    IBKR_AVAILABLE = True
except ImportError:
    IBKR_AVAILABLE = False
    get_ibkr_client = None

logger = logging.getLogger(__name__)


class Bot:
    """
    交易机器人基类
    只负责核心交易功能，不包含冗余的连接管理
    """

    def __init__(
        self,
        initial_capital: float = 10000.0,
        real_trading: bool = False,
        ibkr_config: IBKRConfig = None,
    ):
        """
        初始化机器人

        Args:
            initial_capital: 初始资金
            real_trading: 是否启用真实交易
            ibkr_config: IBKR配置对象
        """
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.uninvested = initial_capital
        self.invested = 0.0
        self.portfolio = {}
        self.trade_log = []
        self.real_trading = real_trading
        self.ibkr_config = ibkr_config or DEFAULT_CONFIG.ibkr

        # 交易队列用于批量执行
        self.trade_queue = []

        logger.info(
            f"机器人初始化: 资金=${initial_capital:,.2f}, "
            f"真实交易={'启用' if real_trading else '禁用'}, "
            f"IBKR端口={self.ibkr_config.port}"
        )

    def transact_capital(self, ticker: str, units: int, price: float, type: str):
        """执行交易操作"""
        if type == "buy":
            cost = units * price
            if cost <= self.uninvested:
                self.uninvested -= cost
                self.invested += cost
                self.portfolio[ticker] = self.portfolio.get(ticker, 0) + units
                
                # 记录交易
                self.trade_log.append({
                    "timestamp": datetime.now(),
                    "ticker": ticker,
                    "action": "BUY",
                    "units": units,
                    "price": price,
                    "cost": cost
                })
                
                # 添加到交易队列
                if self.real_trading:
                    self.trade_queue.append({
                        "action": "BUY",
                        "ticker": ticker,
                        "quantity": units,
                        "price": price
                    })
                    
        elif type == "sell":
            if ticker in self.portfolio and self.portfolio[ticker] >= units:
                revenue = units * price
                self.uninvested += revenue
                self.invested -= units * price  # 使用原始买入价格计算
                self.portfolio[ticker] -= units
                
                if self.portfolio[ticker] == 0:
                    del self.portfolio[ticker]
                
                # 记录交易
                self.trade_log.append({
                    "timestamp": datetime.now(),
                    "ticker": ticker,
                    "action": "SELL",
                    "units": units,
                    "price": price,
                    "revenue": revenue
                })
                
                # 添加到交易队列
                if self.real_trading:
                    self.trade_queue.append({
                        "action": "SELL",
                        "ticker": ticker,
                        "quantity": units,
                        "price": price
                    })

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        分析市场信号并做出交易决策
        子类必须实现此方法
        """
        raise NotImplementedError(f"{self.__class__.__name__} 必须实现 analyze_and_trade 方法")


class Adam(Bot):
    """
    Adam机器人 - 保守型价值投资策略
    专注于买入被低估的股票，采用较为保守的风险控制
    """

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        Adam - 保守价值投资者
        专注于极度低估的股票，风险控制严格
        """
        logger.info("Adam (保守价值投资者) 分析市场信号...")

        target_stocks = []
        decision_factors = []

        # Adam只关注极度低估的股票
        extreme_below = trading_signals.get("extreme_below_trend", [])
        if extreme_below:
            # 选择评分最低的前3只（最被低估）
            top_value = sorted(extreme_below, key=lambda x: x["score"])[:3]
            target_stocks.extend(top_value)
            decision_factors.append(f"极度低估价值股: {len(top_value)}只")

        # 执行卖出策略（保守止盈止损）
        trades_executed = 0
        sell_decisions = []

        for ticker in list(self.portfolio.keys()):
            # 查找买入价格
            buy_price = None
            for log in reversed(self.trade_log):
                if log["ticker"] == ticker and log["action"] == "BUY":
                    buy_price = log["price"]
                    break

            if buy_price:
                # 从当前信号中找到当前价格
                current_price = None
                for category_stocks in trading_signals.values():
                    if isinstance(category_stocks, list):
                        for stock in category_stocks:
                            if stock.get("ticker") == ticker:
                                current_price = stock["current_price"]
                                break
                    if current_price:
                        break

                if current_price:
                    profit_rate = (current_price - buy_price) / buy_price
                    
                    # 保守的止盈止损：3%止盈，10%止损
                    if profit_rate >= 0.03 or profit_rate <= -0.10:
                        units = self.portfolio[ticker]
                        self.transact_capital(ticker, units, current_price, type="sell")
                        trades_executed += 1
                        sell_decisions.append(f"{ticker} 收益率 {profit_rate:.2%}")

        # 执行买入策略
        if target_stocks:
            # 保守分配：保留30%现金
            available_capital = self.uninvested * 0.7
            capital_per_stock = available_capital / len(target_stocks)

            for stock in target_stocks:
                ticker = stock["ticker"]
                price = stock["current_price"]
                
                if ticker not in self.portfolio:  # 只买入新股票
                    units = int(capital_per_stock // price)
                    if units >= 1:
                        self.transact_capital(ticker, units, price, type="buy")
                        trades_executed += 1

        logger.info(f"Adam 完成分析: {trades_executed} 笔交易")

        return {
            "trades_executed": trades_executed,
            "current_prices": {stock["ticker"]: stock["current_price"] for stock in target_stocks},
            "strategy": "保守价值投资",
            "decision_factors": "; ".join(decision_factors) if decision_factors else "未发现合适机会",
            "target_stocks": [stock["ticker"] for stock in target_stocks],
            "sell_decisions": sell_decisions,
            "portfolio_allocation": "1/3 保守分散投资"
        } if target_stocks or trades_executed > 0 else {}


class Betty(Bot):
    """
    Betty机器人 - 激进型动量投资策略
    专注于买入强势上涨的股票，追求高收益但承担更高风险
    """

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        Betty - 激进动量投资者
        专注于强势上涨股票，追求高回报
        """
        logger.info("Betty (激进动量投资者) 分析市场信号...")

        target_stocks = []
        decision_factors = []

        # Betty关注强势上涨股票
        highly_above = trading_signals.get("highly_above_trend", [])
        extreme_above = trading_signals.get("extreme_above_trend", [])
        
        # 合并并选择最强势的股票
        all_above = highly_above + extreme_above
        if all_above:
            # 按评分排序，选择前5只最强势的
            top_momentum = sorted(all_above, key=lambda x: x["score"], reverse=True)[:5]
            target_stocks.extend(top_momentum)
            decision_factors.append(f"强势动量股: {len(top_momentum)}只")

        # 执行卖出策略（激进止盈止损）
        trades_executed = 0
        sell_decisions = []

        for ticker in list(self.portfolio.keys()):
            # 查找买入价格
            buy_price = None
            for log in reversed(self.trade_log):
                if log["ticker"] == ticker and log["action"] == "BUY":
                    buy_price = log["price"]
                    break

            if buy_price:
                # 从当前信号中找到当前价格
                current_price = None
                for category_stocks in trading_signals.values():
                    if isinstance(category_stocks, list):
                        for stock in category_stocks:
                            if stock.get("ticker") == ticker:
                                current_price = stock["current_price"]
                                break
                    if current_price:
                        break

                if current_price:
                    profit_rate = (current_price - buy_price) / buy_price
                    
                    # 激进的止盈止损：10%止盈，3%止损
                    if profit_rate >= 0.10 or profit_rate <= -0.03:
                        units = self.portfolio[ticker]
                        self.transact_capital(ticker, units, current_price, type="sell")
                        trades_executed += 1
                        sell_decisions.append(f"{ticker} 收益率 {profit_rate:.2%}")

        # 执行买入策略
        if target_stocks:
            # 激进分配：保留10%现金
            available_capital = self.uninvested * 0.9
            capital_per_stock = available_capital / len(target_stocks)

            for stock in target_stocks:
                ticker = stock["ticker"]
                price = stock["current_price"]
                
                if ticker not in self.portfolio:  # 只买入新股票
                    units = int(capital_per_stock // price)
                    if units >= 1:
                        self.transact_capital(ticker, units, price, type="buy")
                        trades_executed += 1

        logger.info(f"Betty 完成分析: {trades_executed} 笔交易")

        return {
            "trades_executed": trades_executed,
            "current_prices": {stock["ticker"]: stock["current_price"] for stock in target_stocks},
            "strategy": "激进动量投资",
            "decision_factors": "; ".join(decision_factors) if decision_factors else "未发现合适机会",
            "target_stocks": [stock["ticker"] for stock in target_stocks],
            "sell_decisions": sell_decisions,
            "portfolio_allocation": "1/5 集中投资"
        } if target_stocks or trades_executed > 0 else {}


class Chris(Bot):
    """
    Chris机器人 - 技术分析策略
    专注于技术指标和图表模式
    """

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        Chris - 技术分析专家
        基于技术指标进行交易决策
        """
        logger.info("Chris (技术分析专家) 分析市场信号...")

        target_stocks = []
        decision_factors = []

        # Chris关注技术突破机会
        extreme_above = trading_signals.get("extreme_above_trend", [])
        extreme_below = trading_signals.get("extreme_below_trend", [])

        # 寻找技术突破机会（极端值反转）
        if extreme_below:
            # 选择评分最极端的股票（可能反转）
            reversal_candidates = sorted(extreme_below, key=lambda x: x["score"])[:2]
            target_stocks.extend(reversal_candidates)
            decision_factors.append(f"技术反转机会: {len(reversal_candidates)}只")

        # 执行卖出策略（技术止损）
        trades_executed = 0
        sell_decisions = []

        for ticker in list(self.portfolio.keys()):
            # 查找买入价格
            buy_price = None
            for log in reversed(self.trade_log):
                if log["ticker"] == ticker and log["action"] == "BUY":
                    buy_price = log["price"]
                    break

            if buy_price:
                # 从当前信号中找到当前价格
                current_price = None
                for category_stocks in trading_signals.values():
                    if isinstance(category_stocks, list):
                        for stock in category_stocks:
                            if stock.get("ticker") == ticker:
                                current_price = stock["current_price"]
                                break
                    if current_price:
                        break

                if current_price:
                    profit_rate = (current_price - buy_price) / buy_price

                    # 技术止损：8%止盈，5%止损
                    if profit_rate >= 0.08 or profit_rate <= -0.05:
                        units = self.portfolio[ticker]
                        self.transact_capital(ticker, units, current_price, type="sell")
                        trades_executed += 1
                        sell_decisions.append(f"{ticker} 收益率 {profit_rate:.2%}")

        # 执行买入策略
        if target_stocks:
            # 技术分析：保留25%现金
            available_capital = self.uninvested * 0.75
            capital_per_stock = available_capital / len(target_stocks)

            for stock in target_stocks:
                ticker = stock["ticker"]
                price = stock["current_price"]

                if ticker not in self.portfolio:  # 只买入新股票
                    units = int(capital_per_stock // price)
                    if units >= 1:
                        self.transact_capital(ticker, units, price, type="buy")
                        trades_executed += 1

        logger.info(f"Chris 完成分析: {trades_executed} 笔交易")

        return {
            "trades_executed": trades_executed,
            "current_prices": {stock["ticker"]: stock["current_price"] for stock in target_stocks},
            "strategy": "技术分析",
            "decision_factors": "; ".join(decision_factors) if decision_factors else "未发现合适机会",
            "target_stocks": [stock["ticker"] for stock in target_stocks],
            "sell_decisions": sell_decisions,
            "portfolio_allocation": "1/2 技术突破"
        } if target_stocks or trades_executed > 0 else {}


class Dany(Bot):
    """
    Dany机器人 - 平衡投资策略
    结合价值和趋势，风险容忍度适中
    """

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        Dany - 平衡价值投资者
        结合价值和趋势，风险容忍度适中
        """
        logger.info("Dany (平衡价值投资者) 分析市场信号...")

        target_stocks = []
        decision_factors = []

        # Dany的策略：平衡考虑低估股票和适度趋势
        extreme_below = trading_signals.get("extreme_below_trend", [])
        highly_below = trading_signals.get("highly_below_trend", [])

        # 第一优先级：极度低估股票
        if extreme_below:
            top_extreme = sorted(extreme_below, key=lambda x: x["score"])[:2]
            target_stocks.extend(top_extreme)
            decision_factors.append(f"极度低估机会: {len(top_extreme)}只")

        # 第二优先级：高度低估股票
        if highly_below and len(target_stocks) < 4:
            remaining_slots = 4 - len(target_stocks)
            top_highly = sorted(highly_below, key=lambda x: x["score"])[:remaining_slots]
            target_stocks.extend(top_highly)
            decision_factors.append(f"高度低估机会: {len(top_highly)}只")

        # 执行卖出策略（均衡止盈止损）
        trades_executed = 0
        sell_decisions = []

        for ticker in list(self.portfolio.keys()):
            # 查找买入价格
            buy_price = None
            for log in reversed(self.trade_log):
                if log["ticker"] == ticker and log["action"] == "BUY":
                    buy_price = log["price"]
                    break

            if buy_price:
                # 从当前信号中找到当前价格
                current_price = None
                for category_stocks in trading_signals.values():
                    if isinstance(category_stocks, list):
                        for stock in category_stocks:
                            if stock.get("ticker") == ticker:
                                current_price = stock["current_price"]
                                break
                    if current_price:
                        break

                if current_price:
                    profit_rate = (current_price - buy_price) / buy_price

                    # 均衡的止盈止损：5%止盈，5%止损
                    if profit_rate >= 0.05 or profit_rate <= -0.05:
                        units = self.portfolio[ticker]
                        self.transact_capital(ticker, units, current_price, type="sell")
                        trades_executed += 1
                        sell_decisions.append(f"{ticker} 收益率 {profit_rate:.2%}")

        # 执行买入策略
        if target_stocks:
            # 均衡分配：保留20%现金
            available_capital = self.uninvested * 0.8
            capital_per_stock = available_capital / len(target_stocks)

            for stock in target_stocks:
                ticker = stock["ticker"]
                price = stock["current_price"]

                if ticker not in self.portfolio:  # 只买入新股票
                    units = int(capital_per_stock // price)
                    if units >= 1:
                        self.transact_capital(ticker, units, price, type="buy")
                        trades_executed += 1

        logger.info(f"Dany 完成分析: {trades_executed} 笔交易")

        return {
            "trades_executed": trades_executed,
            "current_prices": {stock["ticker"]: stock["current_price"] for stock in target_stocks},
            "strategy": "平衡价值投资",
            "decision_factors": "; ".join(decision_factors) if decision_factors else "未发现合适机会",
            "target_stocks": [stock["ticker"] for stock in target_stocks],
            "sell_decisions": sell_decisions,
            "portfolio_allocation": "1/4 均衡分散"
        } if target_stocks or trades_executed > 0 else {}


class Eddy(Bot):
    """
    Eddy机器人 - 激进趋势投资策略
    专注于强势趋势股票，高风险高回报
    """

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        Eddy - 激进趋势投资者
        专注于最强势的趋势股票
        """
        logger.info("Eddy (激进趋势投资者) 分析市场信号...")

        target_stocks = []
        decision_factors = []

        # Eddy关注所有上涨趋势股票
        highly_above = trading_signals.get("highly_above_trend", [])
        extreme_above = trading_signals.get("extreme_above_trend", [])

        # 合并所有上涨股票，选择最强势的
        all_above = highly_above + extreme_above
        if all_above:
            # 按评分排序，选择前3只最强势的进行集中投资
            top_trends = sorted(all_above, key=lambda x: x["score"], reverse=True)[:3]
            target_stocks.extend(top_trends)
            decision_factors.append(f"强势趋势股: {len(top_trends)}只")

        # 执行卖出策略（激进止损）
        trades_executed = 0
        sell_decisions = []

        for ticker in list(self.portfolio.keys()):
            # 查找买入价格
            buy_price = None
            for log in reversed(self.trade_log):
                if log["ticker"] == ticker and log["action"] == "BUY":
                    buy_price = log["price"]
                    break

            if buy_price:
                # 从当前信号中找到当前价格
                current_price = None
                for category_stocks in trading_signals.values():
                    if isinstance(category_stocks, list):
                        for stock in category_stocks:
                            if stock.get("ticker") == ticker:
                                current_price = stock["current_price"]
                                break
                    if current_price:
                        break

                if current_price:
                    profit_rate = (current_price - buy_price) / buy_price

                    # 激进的止盈止损：15%止盈，2%止损
                    if profit_rate >= 0.15 or profit_rate <= -0.02:
                        units = self.portfolio[ticker]
                        self.transact_capital(ticker, units, current_price, type="sell")
                        trades_executed += 1
                        sell_decisions.append(f"{ticker} 收益率 {profit_rate:.2%}")

        # 执行买入策略
        if target_stocks:
            # 激进集中投资：保留5%现金
            available_capital = self.uninvested * 0.95
            capital_per_stock = available_capital / len(target_stocks)

            for stock in target_stocks:
                ticker = stock["ticker"]
                price = stock["current_price"]

                if ticker not in self.portfolio:  # 只买入新股票
                    units = int(capital_per_stock // price)
                    if units >= 1:
                        self.transact_capital(ticker, units, price, type="buy")
                        trades_executed += 1

        logger.info(f"Eddy 完成分析: {trades_executed} 笔交易")

        return {
            "trades_executed": trades_executed,
            "current_prices": {stock["ticker"]: stock["current_price"] for stock in target_stocks},
            "strategy": "激进趋势投资",
            "decision_factors": "; ".join(decision_factors) if decision_factors else "未发现合适机会",
            "target_stocks": [stock["ticker"] for stock in target_stocks],
            "sell_decisions": sell_decisions,
            "portfolio_allocation": "1/3 高度集中投资"
        } if target_stocks or trades_executed > 0 else {}


class Flora(Bot):
    """
    Flora机器人 - 成长股投资策略
    专注于稳定趋势中的高成长股票
    """

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        Flora - 成长股投资者
        专注于稳定成长的股票
        """
        logger.info("Flora (成长股投资者) 分析市场信号...")

        target_stocks = []
        decision_factors = []

        # Flora关注稳定趋势中的成长股
        along_trend = trading_signals.get("along_trend", [])
        highly_below = trading_signals.get("highly_below_trend", [])

        # 寻找稳定趋势中的高成长股票
        if along_trend:
            # 计算成长率并筛选
            growth_stocks = []
            for stock in along_trend:
                growth_rate = (stock["predicted_price"] - stock["current_price"]) / stock["current_price"]
                if growth_rate >= 0.5:  # 50%成长预期
                    stock["calculated_growth"] = growth_rate
                    growth_stocks.append(stock)

            if growth_stocks:
                # 按成长率排序，选择前4只
                top_growth = sorted(growth_stocks, key=lambda x: x["calculated_growth"], reverse=True)[:4]
                target_stocks.extend(top_growth)
                decision_factors.append(f"稳定成长股: {len(top_growth)}只")

        # 也考虑一些低估但成长性好的股票
        if highly_below and len(target_stocks) < 6:
            growth_below_stocks = []
            for stock in highly_below:
                growth_rate = (stock["predicted_price"] - stock["current_price"]) / stock["current_price"]
                if growth_rate >= 0.5:  # 50%成长预期
                    stock["calculated_growth"] = growth_rate
                    growth_below_stocks.append(stock)

            if growth_below_stocks:
                remaining_slots = 6 - len(target_stocks)
                top_value_growth = sorted(growth_below_stocks, key=lambda x: x["calculated_growth"], reverse=True)[:remaining_slots]
                target_stocks.extend(top_value_growth)
                decision_factors.append(f"价值成长股: {len(top_value_growth)}只")

        # 执行卖出策略（适度止盈止损）
        trades_executed = 0
        sell_decisions = []

        for ticker in list(self.portfolio.keys()):
            # 查找买入价格
            buy_price = None
            for log in reversed(self.trade_log):
                if log["ticker"] == ticker and log["action"] == "BUY":
                    buy_price = log["price"]
                    break

            if buy_price:
                # 从当前信号中找到当前价格
                current_price = None
                for category_stocks in trading_signals.values():
                    if isinstance(category_stocks, list):
                        for stock in category_stocks:
                            if stock.get("ticker") == ticker:
                                current_price = stock["current_price"]
                                break
                    if current_price:
                        break

                if current_price:
                    profit_rate = (current_price - buy_price) / buy_price

                    # 成长股策略：10%止盈，20%止损
                    if profit_rate >= 0.10 or profit_rate <= -0.20:
                        units = self.portfolio[ticker]
                        self.transact_capital(ticker, units, current_price, type="sell")
                        trades_executed += 1
                        sell_decisions.append(f"{ticker} 收益率 {profit_rate:.2%}")

        # 执行买入策略
        if target_stocks:
            # 成长股投资：保留15%现金
            available_capital = self.uninvested * 0.85

            # 按成长性加权分配资金
            total_growth = sum(stock.get("calculated_growth", 0.5) for stock in target_stocks)

            for stock in target_stocks:
                ticker = stock["ticker"]
                price = stock["current_price"]
                growth = stock.get("calculated_growth", 0.5)

                if ticker not in self.portfolio:  # 只买入新股票
                    # 根据成长性分配资金
                    growth_weight = growth / total_growth if total_growth > 0 else 1.0 / len(target_stocks)
                    position_size = available_capital * growth_weight
                    units = int(position_size // price)

                    if units >= 1:
                        self.transact_capital(ticker, units, price, type="buy")
                        trades_executed += 1

        logger.info(f"Flora 完成分析: {trades_executed} 笔交易")

        return {
            "trades_executed": trades_executed,
            "current_prices": {stock["ticker"]: stock["current_price"] for stock in target_stocks},
            "strategy": "成长股投资",
            "decision_factors": "; ".join(decision_factors) if decision_factors else "未发现合适机会",
            "target_stocks": [stock["ticker"] for stock in target_stocks],
            "sell_decisions": sell_decisions,
            "portfolio_allocation": "成长性加权分配"
        } if target_stocks or trades_executed > 0 else {}
