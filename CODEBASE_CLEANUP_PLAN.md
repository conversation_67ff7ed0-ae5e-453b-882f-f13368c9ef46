# 代码库清理计划

## 🎯 清理目标
1. **统一配置使用**: 确保所有Python文件都使用 `config.py` 中的统一配置
2. **删除示例代码**: 移除所有示例、模拟、测试、演示代码
3. **清理硬编码路径**: 替换所有硬编码的文件路径为配置引用

## 🔍 发现的问题

### 1. 硬编码路径问题

#### 已修复的文件
- ✅ **stocks/trend.py**: 已修复 symbols_file 和 stock_info_file 路径
- ✅ **services/updater.py**: 已修复 stock_info_file 路径
- ✅ **core/market_data.py**: 已修复 stock_info_path 路径
- ✅ **core/cache.py**: 已修复 base_dir 路径
- ✅ **options/monitor.py**: 已修复 results 路径和 IBKR 配置

#### 仍需检查的文件
- **config.py**: 内部的 setup_directories() 方法仍有硬编码路径

### 2. 示例/模拟代码问题

#### 需要清理的文件和内容

##### **options/arbitrage.py** (3022行)
**问题**: 包含大量模拟代码和示例代码
- `_create_simulated_ml_matches()` 方法 (第2435-2563行)
- 模拟市场数据生成 (第960-977行)
- 模拟价格数据生成 (第574行)
- 模拟期权报价 (第1497-1502行)
- 示例使用代码 (第2996-3022行)

**清理策略**:
- 删除所有 `_create_simulated_ml_matches` 相关代码
- 删除模拟数据生成逻辑，改为返回错误或空结果
- 删除文件末尾的示例使用代码 (main函数)
- 保留核心业务逻辑，移除测试/演示功能

##### **options/tracker.py** (841行)
**问题**: 包含示例代码
- 示例使用代码 (第796-841行)

**清理策略**:
- 删除文件末尾的示例使用代码 (main函数)

##### **core/models.py**
**需要检查**: 可能包含示例或测试代码

##### **core/market_data.py**
**需要检查**: 可能包含示例或测试代码

##### **stocks/trend.py**
**需要检查**: 可能包含示例或测试代码

##### **services/updater.py**
**需要检查**: 可能包含示例或测试代码

### 3. 配置统一问题

#### config.py 中的硬编码路径
```python
def setup_directories(self):
    """创建必要的目录"""
    directories = [
        self.system.data_dir,
        self.system.results_dir,
        self.system.logs_dir,
        self.system.cache_dir,
        "data/stock_info",      # 硬编码
        "data/models",          # 硬编码
        "data/cache"            # 硬编码
    ]
```

**修复方案**: 使用配置属性替代硬编码路径

## 📋 清理执行计划

### 阶段1: 完成配置统一 ✅
- [x] 修复 core/cache.py 硬编码路径
- [x] 修复 options/monitor.py 硬编码路径和配置错误
- [ ] 修复 config.py 中的硬编码路径

### 阶段2: 删除示例/模拟代码
- [ ] 清理 options/arbitrage.py 中的模拟代码
- [ ] 清理 options/tracker.py 中的示例代码
- [ ] 检查并清理其他文件中的示例代码

### 阶段3: 全面验证
- [ ] 验证所有文件都使用统一配置
- [ ] 确认没有遗留的示例/模拟代码
- [ ] 测试系统功能完整性

## 🚨 注意事项

### 保留的功能
- **核心业务逻辑**: 保留所有实际交易和分析功能
- **错误处理**: 保留完整的错误处理机制
- **日志记录**: 保留所有日志功能
- **配置管理**: 保留配置系统

### 删除的功能
- **模拟数据生成**: 删除所有模拟市场数据生成
- **示例使用代码**: 删除所有 main() 示例函数
- **测试/演示代码**: 删除所有测试和演示相关代码
- **硬编码路径**: 替换为配置引用

### 替代策略
- **模拟数据** → **返回错误或空结果，要求真实数据**
- **示例代码** → **完全删除**
- **硬编码路径** → **使用 DEFAULT_CONFIG 配置**

## 🎯 预期结果

清理完成后，代码库将具有：
- ✅ **完全统一的配置管理**
- ✅ **纯净的生产代码**
- ✅ **一致的错误处理**
- ✅ **简化的维护流程**
- ✅ **更高的代码质量**

所有文件将只包含生产环境所需的核心功能，不再有任何示例、模拟或测试代码。
